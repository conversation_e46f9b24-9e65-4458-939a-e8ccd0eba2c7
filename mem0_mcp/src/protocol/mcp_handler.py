"""
MCP protocol handler for processing MCP-specific messages
"""

from typing import Any, Dict, List, Optional, Tuple
from ..config.constants import MCP_VERSION, ERROR_CODES, SUPPORTED_MCP_VERSIONS
from ..utils.errors import ProtocolError
from ..utils.logger import get_logger
from .jsonrpc import JSONRPCRequest, JSONRPCResponse, JSONRPCHandler, JSONRPCError
from .message_types import (
    MC<PERSON>essageType,
    InitializeRequest,
    InitializeResponse,
    ServerInfo,
    ServerCapabilities,
    ClientCapabilities,
    ToolsListRequest,
    ToolsListResponse,
    ToolInfo,
    ToolsCallRequest,
    ToolsCallResponse,
    ADD_MEMORY_SCHEMA,
    SEARCH_MEMORIES_SCHEMA,
    GET_MEMORIES_SCHEMA,
    GET_MEMORY_BY_ID_SCHEMA,
    DELETE_MEMORY_SCHEMA,
    UPDATE_MEMORY_SCHEMA,
    BATCH_DELETE_MEMORIES_SCHEMA,
    # Graph Management Schemas have been removed
    SELECTIVE_MEMORY_SCHEMA,
    CRITERIA_RETRIEVAL_SCHEMA
)

logger = get_logger(__name__)


class MCPProtocolHandler:
    """
    Handler for MCP protocol messages and lifecycle
    """
    
    def __init__(self, server_name: str = "mem0-mcp-server", server_version: str = "1.0.0"):
        self.server_name = server_name
        self.server_version = server_version
        self.initialized = False
        self.client_capabilities: Optional[ClientCapabilities] = None
        
        # Define available tools - All 12 tools for complete MCP integration
        self.tools = [
            # Basic Memory Management Tools (7)
            ToolInfo(
                name="add_memory",
                description="💾 智能记忆添加 - 从对话消息中提取并存储记忆，支持自定义分类、时间戳、图谱增强等高级功能",
                inputSchema=ADD_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="search_memories",
                description="🔍 智能记忆搜索 - 使用自然语言查询记忆，支持关键词搜索、语义检索、重排序和图谱关联",
                inputSchema=SEARCH_MEMORIES_SCHEMA
            ),
            ToolInfo(
                name="get_memories",
                description="📋 记忆列表获取 - 按用户、代理或会话获取记忆列表，支持分页和元数据过滤",
                inputSchema=GET_MEMORIES_SCHEMA
            ),
            ToolInfo(
                name="get_memory_by_id",
                description="🎯 精确记忆检索 - 通过唯一ID获取指定记忆的完整详情和元数据",
                inputSchema=GET_MEMORY_BY_ID_SCHEMA
            ),
            ToolInfo(
                name="delete_memory",
                description="🗑️ 记忆删除 - 安全删除指定ID的记忆，支持软删除和永久删除模式",
                inputSchema=DELETE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="update_memory",
                description="✏️ 记忆内容更新 - 修改现有记忆的内容、元数据和分类信息",
                inputSchema=UPDATE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="batch_delete_memories",
                description="🧹 批量记忆清理 - 按用户、代理或会话批量删除相关记忆，支持条件过滤",
                inputSchema=BATCH_DELETE_MEMORIES_SCHEMA
            ),
            
            # Advanced Memory Management Tools (2)
            ToolInfo(
                name="selective_memory",
                description="🧠 智能选择性记忆 - 基于重要性评估的智能记忆管理，自动过滤、评估和选择性存储重要信息",
                inputSchema=SELECTIVE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="criteria_retrieval",
                description="🎛️ 多维条件检索 - 使用复杂条件进行记忆检索，支持时间范围、内容长度、关键词等多维度过滤",
                inputSchema=CRITERIA_RETRIEVAL_SCHEMA
            )
        ]
        
        # Define available prompts for MCP compliance
        self.prompts = [
            {
                "name": "create_memory_prompt",
                "title": "Create Memory Prompt",
                "description": "Generate a structured prompt for creating memories from conversation context",
                "arguments": [
                    {
                        "name": "context",
                        "description": "The conversation context to create memories from",
                        "required": True
                    },
                    {
                        "name": "user_id",
                        "description": "User identifier for the memory",
                        "required": False
                    }
                ]
            },
            {
                "name": "search_memory_prompt",
                "title": "Search Memory Prompt", 
                "description": "Generate a search prompt for finding relevant memories",
                "arguments": [
                    {
                        "name": "query",
                        "description": "The search query",
                        "required": True
                    },
                    {
                        "name": "context",
                        "description": "Additional context for the search",
                        "required": False
                    }
                ]
            },
            {
                "name": "analyze_memory_prompt",
                "title": "Analyze Memory Prompt",
                "description": "Generate a prompt for analyzing memory patterns and relationships",
                "arguments": [
                    {
                        "name": "memories",
                        "description": "Memory data to analyze",
                        "required": True
                    },
                    {
                        "name": "analysis_type",
                        "description": "Type of analysis to perform",
                        "required": False
                    }
                ]
            }
        ]
        
        logger.info(f"MCP Protocol Handler initialized for {server_name} v{server_version}")
    
    def negotiate_protocol_version(self, client_version: str) -> str:
        """
        Negotiate protocol version with client
        
        Args:
            client_version: Client's requested protocol version
            
        Returns:
            Agreed protocol version
        """
        logger.debug(f"Negotiating protocol version. Client: {client_version}, Supported: {SUPPORTED_MCP_VERSIONS}")
        
        # If client version is supported, use it
        if client_version in SUPPORTED_MCP_VERSIONS:
            logger.info(f"Using client's protocol version: {client_version}")
            return client_version
        
        # Otherwise, use server's default and warn
        logger.warning(f"Client protocol version {client_version} not supported. Using server version {MCP_VERSION}")
        return MCP_VERSION
    
    def get_server_capabilities(self) -> ServerCapabilities:
        """Get server capabilities"""
        return ServerCapabilities(
            tools={"listChanged": True},      # Support tools with listChanged notifications
            resources={},                     # No resources supported but declare the capability  
            prompts={"listChanged": True},    # Support prompts with listChanged notifications
            logging={}                        # No logging callbacks supported
        )
    
    def handle_initialize(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle initialize request
        
        Args:
            request: Initialize request
            
        Returns:
            Initialize response
        """
        try:
            if not request.params or not isinstance(request.params, dict):
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Initialize request must have params object",
                    id=request.id
                )
            
            # Parse client info and capabilities
            protocol_version = request.params.get("protocolVersion")
            if not protocol_version:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "protocolVersion is required",
                    id=request.id
                )
            
            # Negotiate protocol version
            agreed_version = self.negotiate_protocol_version(protocol_version)
            
            client_info = request.params.get("clientInfo", {})
            client_capabilities = request.params.get("capabilities", {})
            
            # Store client capabilities
            self.client_capabilities = ClientCapabilities(
                sampling=client_capabilities.get("sampling"),
                experimental=client_capabilities.get("experimental")
            )
            
            # Create response
            response = InitializeResponse(
                protocolVersion=agreed_version,
                capabilities=self.get_server_capabilities(),
                serverInfo=ServerInfo(
                    name=self.server_name,
                    version=self.server_version
                )
            )
            
            self.initialized = True
            logger.info(f"Initialized session with client: {client_info.get('name', 'unknown')}")
            
            return JSONRPCHandler.create_response(
                result=response.to_dict(),
                id=request.id
            )
            
        except Exception as e:
            logger.error(f"Error handling initialize: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error during initialization: {str(e)}",
                id=request.id
            )
    
    def handle_tools_list(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle tools/list request
        
        Args:
            request: Tools list request
            
        Returns:
            Tools list response
        """
        if not self.initialized:
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INVALID_REQUEST"],
                "Session not initialized",
                id=request.id
            )
        
        try:
            response = ToolsListResponse(tools=self.tools)
            
            logger.debug(f"Returning {len(self.tools)} available tools")
            
            return JSONRPCHandler.create_response(
                result=response.to_dict(),
                id=request.id
            )
            
        except Exception as e:
            logger.error(f"Error handling tools/list: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error listing tools: {str(e)}",
                id=request.id
            )
    
    async def handle_tools_call(self, request: JSONRPCRequest, tool_executor) -> JSONRPCResponse:
        """
        Handle tools/call request
        
        Args:
            request: Tools call request
            tool_executor: Tool executor instance
            
        Returns:
            Tools call response
        """
        if not self.initialized:
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INVALID_REQUEST"],
                "Session not initialized",
                id=request.id
            )
        
        try:
            if not request.params or not isinstance(request.params, dict):
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Tools call request must have params object",
                    id=request.id
                )
            
            tool_name = request.params.get("name")
            arguments = request.params.get("arguments", {})
            
            # Handle special case: Cherry Studio might send tool parameters directly in params
            # without the standard MCP "name" and "arguments" structure
            if not tool_name and "query" in request.params:
                # This looks like a direct tool call, try to infer the tool name
                # Could be search_memories or other search tools
                tool_name = "search_memories"  # Default to memory search
                arguments = request.params.copy()
                logger.info(f"Detected direct tool call format, inferred tool: {tool_name}")
            
            # Extract identity parameters from top-level params (Cherry Studio format)
            # and merge them into arguments for tool execution
            identity_params = ["user_id", "agent_id", "run_id", "session_id"]
            extracted_identity = {}
            
            # First, extract from top-level params (Cherry Studio format)
            for param in identity_params:
                if param in request.params and param not in arguments:
                    arguments[param] = request.params[param]
                    extracted_identity[param] = request.params[param]
            
            # Then, check if identity parameters exist in arguments and should be overridden by context
            # This handles the case where frontend sends identity in arguments but URL path has different identity
            context_identity = None
            try:
                from ..identity.context_manager import IdentityManager
                context_identity = IdentityManager.get_current_identity()
                if context_identity.is_valid():
                    logger.info(f"Context identity found: {context_identity}")
                    # Override arguments with context identity if context is valid
                    if context_identity.user_id and context_identity.user_id != arguments.get('user_id'):
                        logger.info(f"Overriding user_id from '{arguments.get('user_id')}' to '{context_identity.user_id}'")
                        arguments['user_id'] = context_identity.user_id
                    if context_identity.agent_id and context_identity.agent_id != arguments.get('agent_id'):
                        logger.info(f"Overriding agent_id from '{arguments.get('agent_id')}' to '{context_identity.agent_id}'")
                        arguments['agent_id'] = context_identity.agent_id
                    if context_identity.run_id and context_identity.run_id != arguments.get('run_id'):
                        logger.info(f"Overriding run_id from '{arguments.get('run_id')}' to '{context_identity.run_id}'")
                        arguments['run_id'] = context_identity.run_id
            except Exception as e:
                logger.debug(f"Could not get context identity: {e}")
            
            logger.info(f"Tool call: {tool_name}")
            logger.info(f"Original params: {request.params}")
            logger.info(f"Extracted identity: {extracted_identity}")
            logger.info(f"Final arguments: {arguments}")
            
            if not tool_name:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Tool name is required",
                    id=request.id
                )
            
            # Check if tool exists
            tool_names = [tool.name for tool in self.tools]
            if tool_name not in tool_names:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["METHOD_NOT_FOUND"],
                    f"Tool '{tool_name}' not found. Available tools: {', '.join(tool_names)}",
                    id=request.id
                )
            
            logger.debug(f"Executing tool: {tool_name} with arguments: {arguments}")
            
            # Execute tool asynchronously
            return await tool_executor.execute_tool(tool_name, arguments, request.id)
            
        except Exception as e:
            logger.error(f"Error handling tools/call: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error executing tool: {str(e)}",
                id=request.id
            )
    
    def handle_ping(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle ping request - simple health check

        Args:
            request: Ping request

        Returns:
            Ping response
        """
        try:
            logger.debug("Handling ping request")
            return JSONRPCHandler.create_response(
                result={"status": "ok", "message": "Mem0 MCP Server is running"},
                id=request.id
            )
        except Exception as e:
            logger.error(f"Error handling ping: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error handling ping: {str(e)}",
                id=request.id
            )

    def handle_prompts_list(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle prompts/list request - return available prompts

        Args:
            request: Prompts list request

        Returns:
            Prompts list response
        """
        try:
            logger.debug(f"Handling prompts/list request - returning {len(self.prompts)} prompts")
            
            # Support pagination if cursor is provided
            cursor = None
            if request.params and isinstance(request.params, dict):
                cursor = request.params.get("cursor")
            
            # Simple pagination: if cursor is provided, it should be a number
            start_idx = 0
            if cursor:
                try:
                    start_idx = int(cursor)
                except (ValueError, TypeError):
                    start_idx = 0
            
            # Return up to 10 prompts per page
            page_size = 10
            end_idx = start_idx + page_size
            page_prompts = self.prompts[start_idx:end_idx]
            
            # Determine next cursor
            next_cursor = None
            if end_idx < len(self.prompts):
                next_cursor = str(end_idx)
            
            result = {"prompts": page_prompts}
            if next_cursor:
                result["nextCursor"] = next_cursor
                
            return JSONRPCHandler.create_response(
                result=result,
                id=request.id
            )
        except Exception as e:
            logger.error(f"Error handling prompts/list: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error handling prompts/list: {str(e)}",
                id=request.id
            )

    def handle_prompts_get(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle prompts/get request - return a specific prompt with arguments applied

        Args:
            request: Prompts get request

        Returns:
            Prompts get response
        """
        try:
            if not request.params or not isinstance(request.params, dict):
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "prompts/get request must have params object",
                    id=request.id
                )
            
            prompt_name = request.params.get("name")
            arguments = request.params.get("arguments", {})
            
            if not prompt_name:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Prompt name is required",
                    id=request.id
                )
            
            # Find the prompt
            prompt = None
            for p in self.prompts:
                if p["name"] == prompt_name:
                    prompt = p
                    break
            
            if not prompt:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    f"Prompt '{prompt_name}' not found",
                    id=request.id
                )
            
            # Validate required arguments
            for arg in prompt.get("arguments", []):
                if arg.get("required", False) and arg["name"] not in arguments:
                    return JSONRPCHandler.create_error_response(
                        ERROR_CODES["INVALID_PARAMS"],
                        f"Required argument '{arg['name']}' is missing",
                        id=request.id
                    )
            
            # Generate the prompt based on the type
            messages = self._generate_prompt_messages(prompt_name, arguments)
            
            result = {
                "description": prompt["description"],
                "messages": messages
            }
                
            logger.debug(f"Generated prompt '{prompt_name}' with {len(messages)} messages")
            return JSONRPCHandler.create_response(
                result=result,
                id=request.id
            )
            
        except Exception as e:
            logger.error(f"Error handling prompts/get: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error handling prompts/get: {str(e)}",
                id=request.id
            )

    def _generate_prompt_messages(self, prompt_name: str, arguments: dict) -> list:
        """
        Generate prompt messages based on prompt type and arguments
        
        Args:
            prompt_name: Name of the prompt
            arguments: Arguments provided by the client
            
        Returns:
            List of prompt messages in MCP format
        """
        if prompt_name == "create_memory_prompt":
            context = arguments.get("context", "")
            user_id = arguments.get("user_id", "user")
            
            return [
                {
                    "role": "user",
                    "content": {
                        "type": "text",
                        "text": f"Please analyze the following conversation context and extract meaningful memories:\n\nContext: {context}\n\nFor user: {user_id}\n\nExtract key information, facts, preferences, and relationships that should be remembered for future interactions."
                    }
                }
            ]
            
        elif prompt_name == "search_memory_prompt":
            query = arguments.get("query", "")
            context = arguments.get("context", "")
            
            prompt_text = f"Search for memories related to: {query}"
            if context:
                prompt_text += f"\n\nAdditional context: {context}"
            prompt_text += "\n\nFind relevant memories and explain why they match the query."
            
            return [
                {
                    "role": "user",
                    "content": {
                        "type": "text",
                        "text": prompt_text
                    }
                }
            ]
            
        elif prompt_name == "analyze_memory_prompt":
            memories = arguments.get("memories", "")
            analysis_type = arguments.get("analysis_type", "general")
            
            prompt_text = f"Analyze the following memories:\n\n{memories}\n\n"
            
            if analysis_type == "patterns":
                prompt_text += "Focus on identifying patterns, trends, and recurring themes."
            elif analysis_type == "relationships":
                prompt_text += "Focus on relationships between people, concepts, and entities."
            elif analysis_type == "timeline":
                prompt_text += "Focus on temporal patterns and chronological development."
            else:
                prompt_text += "Provide a general analysis covering patterns, relationships, and insights."
            
            return [
                {
                    "role": "user",
                    "content": {
                        "type": "text",
                        "text": prompt_text
                    }
                }
            ]
        
        # Default fallback
        return [
            {
                "role": "user",
                "content": {
                    "type": "text",
                    "text": f"Execute prompt: {prompt_name}"
                }
            }
        ]

    def handle_resources_list(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle resources/list request - return empty list as we don't support resources

        Args:
            request: Resources list request

        Returns:
            Empty resources list response
        """
        try:
            logger.debug("Handling resources/list request - returning empty list")
            return JSONRPCHandler.create_response(
                result={"resources": []},
                id=request.id
            )
        except Exception as e:
            logger.error(f"Error handling resources/list: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error handling resources/list: {str(e)}",
                id=request.id
            )

    async def handle_message(self, request: JSONRPCRequest, tool_executor=None) -> Optional[JSONRPCResponse]:
        """
        Route MCP message to appropriate handler

        Args:
            request: JSON-RPC request
            tool_executor: Tool executor for handling tool calls

        Returns:
            JSON-RPC response (None for notifications)
        """
        method = request.method

        # Handle notifications (no response expected)
        if JSONRPCHandler.is_notification(request):
            if method == MCPMessageType.INITIALIZED.value:
                logger.info("Client sent initialized notification")
                return None
            elif method == "notifications/initialized":
                logger.info("Client sent notifications/initialized")
                return None
            else:
                logger.debug(f"Unhandled notification method: {method}")
                return None

        # Handle requests (response expected)
        if method == MCPMessageType.INITIALIZE.value:
            return self.handle_initialize(request)

        elif method == MCPMessageType.TOOLS_LIST.value:
            return self.handle_tools_list(request)

        elif method == MCPMessageType.TOOLS_CALL.value:
            if tool_executor is None:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INTERNAL_ERROR"],
                    "Tool executor not available",
                    id=request.id
                )
            return await self.handle_tools_call(request, tool_executor)

        # Handle additional MCP methods for better client compatibility
        elif method == "ping":
            return self.handle_ping(request)

        elif method == MCPMessageType.PROMPTS_LIST.value:
            return self.handle_prompts_list(request)

        elif method == MCPMessageType.PROMPTS_GET.value:
            return self.handle_prompts_get(request)

        elif method == MCPMessageType.RESOURCES_LIST.value:
            return self.handle_resources_list(request)

        else:
            # Check if this is a standard MCP method that we just don't implement yet
            standard_mcp_methods = [
                "resources/read", "resources/subscribe", "resources/unsubscribe",
                "prompts/get", "logging/setLevel",
                "completion/complete", "sampling/createMessage"
            ]
            
            if method in standard_mcp_methods:
                logger.debug(f"Standard MCP method not implemented: {method}")
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["METHOD_NOT_FOUND"],
                    f"Method '{method}' not implemented",
                    id=request.id
                )
            else:
                logger.warning(f"Unknown method: {method}")
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["METHOD_NOT_FOUND"],
                    f"Method '{method}' not found",
                    id=request.id
                )