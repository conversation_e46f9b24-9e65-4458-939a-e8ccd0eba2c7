{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose ps)", "<PERSON><PERSON>(docker compose:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker cp:*)", "Ba<PERSON>(docker logs mem0-api --tail 10)", "mcp__sequential-thinking__sequentialthinking", "mcp__chrome-mcp__chrome_navigate", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_click_element", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(docker logs:*)", "Bash(ls:*)", "Bash(docker system prune:*)", "mcp__firecrawl-mcp__firecrawl_scrape", "mcp__firecrawl-mcp__firecrawl_search", "<PERSON><PERSON>(docker-compose:*)", "Bash(pip cache:*)", "Bash(pip install:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我对海鲜过敏，不能吃虾蟹\"\"}],\n    \"\"user_id\"\": \"\"test_user\"\"\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"query\"\": \"\"饮食偏好\"\",\n    \"\"user_id\"\": \"\"test_user\"\",\n    \"\"keyword_search\"\": true\n  }')", "mcp__shrimp-task-manager__list_tasks", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"I love playing tennis on weekends\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"test_user\"\"\n}')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON> and I love pizza\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1672531200,\n    \"\"infer\"\": false\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I visited Paris in March 2024\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1709251200\n  }')", "<PERSON><PERSON>(cat:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d @/tmp/multimodal_doc_test.json)", "Bash(node:*)", "Bash(pip3 install Pillow)", "Bash(./run-standalone.sh run -d)", "<PERSON><PERSON>(timeout:*)", "Bash(cp:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(docker volume inspect:*)", "Bash(docker volume:*)", "Bash(docker container logs:*)", "Bash(ss:*)", "Bash(kill:*)", "<PERSON><PERSON>(sudo mkdir:*)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0/data)", "Bash(sudo chmod -R 755 /var/lib/mem0/data)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0)", "Bash(sudo chmod -R 755 /var/lib/mem0)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"pdf_url\"\",\n          \"\"pdf_url\"\": {\n            \"\"url\"\": \"\"https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_pdf_user2\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"pdf_document\"\", \"\"source\"\": \"\"adobe_sample\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"mdx_url\"\",\n          \"\"mdx_url\"\": {\n            \"\"url\"\": \"\"data:text/plain;base64,6L+Z5piv5LiA5Liq5oqA5pyv5paH5qGj55qE5YaF5a6544CC5paH5qGj5Lit5YyF5ZCr5Lul5LiL6YeN6KaB5L+h5oGv77yaCjEuIOS6uuW3peaZuuiDveeahOWfuuacrOamguW/teWSjOWPkeWxleWOhueoiwoyLiDmnLrlmajlrabkuaDnrpfms5XnmoTliIbnsbvvvJrnm5HnnaPlrabkuaDjgIHml6Dnm5HnnaPlrabkuaDjgIHlvLrljJblrabkuaAKMy4g5rex5bqm5a2m5Lmg5Zyo5Zu+5YOP6K+G5Yir44CB6Ieq54S26K+t6KiA5aSE55CG5Lit55qE5bqU55SoCjQuIOWkp+Wei+ivreiogOaooeWei+eahOW3peS9nOWOn+eQhuWSjOS8mOWMluaWueazlQo1LiBBSeS8pueQhuWSjOWuieWFqOaAp+iAg+iZkQoK6L+Z5Lqb5YaF5a655a+55LqO55CG6Kej546w5LujQUnmioDmnK/pnZ7luLjph43opoHjgIIK\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_mdx_user\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"mdx_document\"\", \"\"source\"\": \"\"base64_text\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"query\"\": \"\"人工智能和机器学习的技术概念\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 10\n}')", "Bash(-H \"Content-Type: application/json\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": [\n        {\n          \"\"type\"\": \"\"text\"\",\n          \"\"text\"\": \"\"我正在学习Mem0记忆系统的架构和核心概念。Mem0是一个智能记忆管理平台，具有以下特点：1) 支持多种LLM提供商如OpenAI、Anthropic等；2) 具备向量存储和语义搜索功能；3) 支持用户、代理和会话级别的记忆作用域；4) 提供RESTful API接口；5) 支持多模态内容处理包括文本、图像和文档。\"\"\n        }\n      ]\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"今天我了解到Mem0是一个强大的记忆管理系统，支持多模态内容处理。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"平静放松的感受\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 3\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"Python机器学习\"\",\n  \"\"user_id\"\": \"\"test_memory_id_user\"\",\n  \"\"limit\"\": 3\n}')", "<PERSON><PERSON>(echo:*)", "Bash(echo -e \"\\n\\n测试3: 存在的UUID\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"我今天参加了一个重要的商务会议，讨论了新项目的合作方案。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"category_test_user\"\",\n  \"\"custom_categories\"\": [\n    {\"\"business\"\": \"\"商务相关活动和决策\"\"},\n    {\"\"meetings\"\": \"\"会议和讨论记录\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我报名了一个Python编程课程，想要深入学习人工智能技术\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\",\n  \"\"custom_categories\"\": [\n    {\"\"学习\"\": \"\"学习计划、教育相关\"\"},\n    {\"\"编程\"\": \"\"编程语言、代码开发\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我在网上购物平台买了一本关于深度学习的书籍\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\"\n}')", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__chrome-mcp__chrome_screenshot", "mcp__chrome-mcp__chrome_console", "Bash(npm run dev:*)", "mcp__chrome-mcp__get_windows_and_tabs", "mcp__chrome-mcp__chrome_network_debugger_start", "mcp__chrome-mcp__chrome_network_debugger_stop", "mcp__chrome-mcp__chrome_network_request", "Bash(npm run build:*)", "mcp__chrome-mcp__chrome_inject_script", "mcp__chrome-mcp__chrome_get_interactive_elements", "mcp__chrome-mcp__chrome_network_capture_start", "mcp__chrome-mcp__chrome_network_capture_stop", "mcp__chrome-mcp__chrome_send_command_to_inject_script", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"<PERSON> works at OpenAI as a software engineer. She collaborates with <PERSON> on machine learning projects.\"\"}],\n    \"\"user_id\"\": \"\"demo_user\"\"\n  }')", "Bash(docker stack:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm mem0-api mem0-neo4j mem0-qdrant)", "Bash(docker network:*)", "Bash(# 设置mem0数据目录权限（对应容器内mem0用户，一般UID 1000）\nchown -R 1000:1000 /opt/mem0ai/data/mem0\n\n# 设置qdrant数据目录权限（qdrant容器默认用户）\nchown -R 1000:1000 /opt/mem0ai/data/qdrant  \n\n# 设置neo4j数据目录权限（neo4j容器默认UID 7474）\nchown -R 7474:7474 /opt/mem0ai/data/neo4j\n\n# 设置目录权限\nchmod -R 755 /opt/mem0ai/data/\n\nls -la /opt/mem0ai/data/)", "Bash(/opt/mem0ai/setup-permissions.sh)", "Bash(chown -R root:root /opt/mem0ai/data/mem0 /opt/mem0ai/data/qdrant)", "Bash(/opt/mem0ai/server/deploy.sh start)", "Bash(./deploy.sh start --no-permissions)", "Bash(sudo ./deploy-v2.sh start)", "<PERSON><PERSON>(sudo docker compose:*)", "Bash(sudo docker system prune:*)", "Bash(./deploy-v2.sh:*)", "<PERSON><PERSON>(sudo docker:*)", "Bash(sudo chown:*)", "Bash(sudo chmod:*)", "Bash(hatch run lint:*)", "Bash(make lint:*)", "Bash(-H \"accept: application/json\" )", "Bash(-G -d \"limit=100\")", "Bash(-H \"accept: application/json\" )", "Bash(-d '{}')", "Bash(sudo rm -rf data/*)", "Bash(-H \"accept: application/json\")", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>. I love cats and work as a designer.\"\"}],\n    \"\"user_id\"\": \"\"alice\"\"\n  }')", "Bash(sudo rm -f /opt/mem0ai/data/history.db /opt/mem0ai/data/mem0/history.db)", "WebFetch(domain:localhost)", "Bash(./verify-setup.sh:*)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我喜欢吃意大利面和寿司\"\"},\n      {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"我记住了你喜欢意大利面和寿司\"\"}\n    ],\n    \"\"user_id\"\": \"\"test_user_001\"\",\n    \"\"custom_categories\"\": [\n      {\"\"food_preference\"\": \"\"用户的食物偏好和饮食习惯\"\"},\n      {\"\"personal_taste\"\": \"\"个人品味和喜好\"\"}\n    ]\n  }')", "Bash(if [ -f /opt/mem0ai/server/.env ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(fi)", "Bash(__NEW_LINE__ echo \"\")", "Bash(if [ -f /opt/mem0ai/server/.env.prod ])", "Bash(./deploy.sh:*)", "Bash(./test_data_validation_control.sh:*)", "Bash(./cleanup_uncategorized_memories.sh)", "Bash(for id in \"00b3ae5e-1dfe-476c-8603-253677736e76\" \"3a030fbc-67e7-410d-b2ab-2c8df21b0d1f\" \"52afd10b-faeb-4cbd-b5dd-1634adb1defb\" \"90b0e255-5105-4a06-a1f2-302e0e1718ca\" \"94e65b90-4e6a-41a9-84d8-fc74729f3e7d\" \"bf945be4-3a2c-4739-afdf-4ea49b72cf7e\" \"faf6d575-508c-464f-819d-6c5e97e4b677\")", "Bash(do)", "Bash(if [ $? -eq 0 ])", "Bash(done)", "Bash(./batch_delete_uncategorized.sh)", "Bash(/tmp/analyze_endpoints.sh:*)", "Bash(./fix_api_endpoints.sh:*)", "Bash(bash:*)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I love hiking in the mountains\"\"},\n      {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"That sounds wonderful! What is your favorite hiking trail?\"\"},\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I enjoy the Pacific Crest Trail\"\"}\n    ],\n    \"\"user_id\"\": \"\"auto_test_user\"\"\n  }')", "Bash(for i in {1..4})", "Bash(do curl -s -X POST \"http://localhost:8000/v1/memories/\" -H \"Content-Type: application/json\" -d @/tmp/memory$i.json)", "Bash(-d @/tmp/feedback_very_negative.json)", "Bash(-d @/tmp/multimodal_search_pizza.json)", "Bash(-d @/tmp/multimodal_simple_test.json)", "Bash(-d @/tmp/search_cross_modal.json)", "<PERSON><PERSON>(printenv)", "Bash(OPENAI_API_KEY=dummy python3 -c \"\nimport sys\nsys.path.append(''/opt/mem0ai'')\nfrom mem0 import Memory\n\ntry:\n    m = Memory()\n    print(''Embedder config type:'', type(m.config.embedder.config))\n    print(''Embedder config:'', m.config.embedder.config)\n    if hasattr(m.config.embedder.config, ''model''):\n        print(''Has model attribute:'', m.config.embedder.config.model)\n    else:\n        print(''No model attribute, checking keys:'', list(m.config.embedder.config.keys()) if hasattr(m.config.embedder.config, ''keys'') else ''Not dict-like'')\n        print(''Dict value for model:'', m.config.embedder.config.get(''model'', ''KEY_NOT_FOUND''))\nexcept Exception as e:\n    print(''Error:'', e)\n    import traceback\n    traceback.print_exc()\n\")", "Bash(git checkout:*)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.mem0.ai)", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"测试修复后的embedding配置\"\"}],\n    \"\"user_id\"\": \"\"test_fix_user\"\"\n  }')", "Bash(-d '{\"\"data\"\": \"\"Updated test memory\"\", \"\"metadata\"\": {\"\"test\"\": true}}' )", "Bash(-w \"\\nHTTP Status: %{http_code}\\n\")", "Bash(-d '{\"\"text\"\": \"\"Updated test memory\"\", \"\"metadata\"\": {\"\"test\"\": true}}' )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"测试添加操作调试\"\"}],\n    \"\"user_id\"\": \"\"debug_test_user\"\"\n  }' )", "Bash(-d \"{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"今天是$(date)，我正在测试历史记录功能\"\"}],\n    \"\"user_id\"\": \"\"history_test_user_$(date +%s)\"\"\n  }\")", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我的生日是1990年5月15日，我很喜欢天文学\"\"}],\n    \"\"user_id\"\": \"\"debug_personal_info\"\"\n  }')", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"最终测试：我喜欢喝咖啡，每天早晨都需要一杯拿铁\"\"}],\n    \"\"user_id\"\": \"\"final_test_user\"\"\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"这是为default用户添加的第1条测试记忆，用于验证分页功能\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"default\"\"\n  }')", "mcp__shrimp-task-manager__plan_task", "mcp__shrimp-task-manager__process_thought", "mcp__shrimp-task-manager__analyze_task", "mcp__shrimp-task-manager__reflect_task", "mcp__shrimp-task-manager__split_tasks", "mcp__shrimp-task-manager__execute_task", "mcp__shrimp-task-manager__update_task", "WebFetch(domain:modelcontextprotocol.io)", "mcp__shrimp-task-manager__verify_task", "mcp__shrimp-task-manager__get_task_detail", "Bash(sed -i 's/OpenMemory-style/Context-aware/g' README.md)", "<PERSON><PERSON>(sed:*)", "Bash(-d '{\n    \"\"query\"\": \"\"人工智能相关技术\"\",\n    \"\"search_type\"\": \"\"mixed\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"人工智能相关技术\"\",\n    \"\"search_type\"\": \"\"semantic\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"最终测试修复\"\",\n    \"\"search_type\"\": \"\"semantic\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"label\"\": \"\"Sarah\"\",\n    \"\"type\"\": \"\"Person\"\",\n    \"\"properties\"\": {\n      \"\"age\"\": 28,\n      \"\"occupation\"\": \"\"Software Engineer\"\",\n      \"\"company\"\": \"\"Microsoft\"\",\n      \"\"interests\"\": [\"\"reading\"\", \"\"technology\"\", \"\"books\"\"]\n    }\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"<PERSON> and <PERSON> are siblings. <PERSON> mentors young developers at Microsoft, while <PERSON> collaborates with researchers at Harvard on heart disease studies.\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"metadata\"\": {\"\"source\"\": \"\"relationships\"\", \"\"enable_graph\"\": true}\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"My sister <PERSON> works at Microsoft as a Software Engineer. She is 28 years old and lives in Seattle.\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"My brother <PERSON> is a cardiologist at Boston General Hospital. He graduated from Harvard Medical School and is married to <PERSON>, a nurse.\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"graph_test_user\"\", \n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"<PERSON>\"\",\n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"Sarah Microsoft\"\",\n    \"\"search_type\"\": \"\"semantic\"\", \n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"Alice\"\",\n    \"\"search_type\"\": \"\"semantic\"\", \n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"<PERSON>\"\",\n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\",\n    \"\"limit\"\": 3\n  }')", "Bash(-G -d \"user_id=graph_consistency_test\" -d \"limit=1\")", "Bash(-d '{\n    \"\"query\"\": \"\"alex\"\",\n    \"\"user_id\"\": \"\"graph_cleanup_optimization_test\"\",\n    \"\"enable_graph\"\": true,\n    \"\"limit\"\": 10\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"调试测试：Kevin是一名设计师，在Meta工作。\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"debug_graph_cleanup_test\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"lisa\"\",\n    \"\"user_id\"\": \"\"final_cleanup_test\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-G -d \"user_id=final_cleanup_test\" -d \"limit=5\")", "Bash(-G -d \"user_id=final_cleanup_test\" -d \"limit=10\")", "Bash(-G -d \"user_id=final_cleanup_test\" -d \"limit=3\")", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"<PERSON> works at OpenAI as a machine learning engineer. She collaborates with <PERSON> who is a researcher at Stanford.\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"test_graph_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"<PERSON>\"\",\n    \"\"user_id\"\": \"\"graph_test_user\"\", \n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(__NEW_LINE__ echo -e \"\\n=== 直接测试图谱搜索 ===\")", "Bash(mv retrieve_memory.py retrieve_memory.yaml ../deprecated/)", "<PERSON><PERSON>(mv:*)", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"测试添加记忆：今天是2025年8月3日，我正在验证Mem0活动时间线功能\"\"}],\n    \"\"user_id\"\": \"\"test_user_timeline\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"验证功能\"\",\n    \"\"user_id\"\": \"\"test_user_timeline\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"text\"\": \"\"正在验证Mem0活动时间线功能 - 已更新测试内容\"\"\n  }')", "<PERSON><PERSON>(true)", "mcp__chrome-mcp__chrome_keyboard", "Bash(./deploy-mcp-only.sh:*)", "Bash(docker port:*)", "<PERSON><PERSON>(docker start:*)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"<PERSON> works at OpenAI as a machine learning engineer. She collaborates with <PERSON> who is a researcher at Stanford.\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"final_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"Alice\"\",\n    \"\"user_id\"\": \"\"final_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-d '{\"\"query\"\": \"\"Alice\"\", \"\"user_id\"\": \"\"final_test_user\"\"}')", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我是素食主义者，对坚果过敏\"\"}],\n    \"\"user_id\"\": \"\"alex_test\"\"\n  }')", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我喜欢晚上7点吃晚餐\"\"}],\n    \"\"user_id\"\": \"\"alex_test\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"我的饮食偏好是什么？\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"我的饮食偏好是什么？\"\",\n    \"\"filters\"\": {\"\"user_id\"\": \"\"alex_test\"\"},\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"我的饮食偏好是什么？\"\",\n    \"\"filters\"\": {\"\"user_id\"\": \"\"alex_test\"\"},\n    \"\"limit\"\": 5,\n    \"\"rerank\"\": true\n  }')", "Bash(-d '{\n    \"\"messages\"\": [\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"<PERSON>, I am <PERSON> and I live in San Francisco.\"\"},\n      {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"Hello <PERSON>! Nice to meet you. San Francisco is a beautiful city.\"\"}\n    ],\n    \"\"user_id\"\": \"\"alex_contextual_test\"\",\n    \"\"version\"\": \"\"v2\"\"\n  }')", "Bash(-d '{\n    \"\"method\"\": \"\"tools/call\"\",\n    \"\"params\"\": {\n      \"\"name\"\": \"\"add_memory\"\",\n      \"\"arguments\"\": {\n        \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I am <PERSON> and I work as a data scientist at Google.\"\"}],\n        \"\"user_id\"\": \"\"sarah_test\"\",\n        \"\"version\"\": \"\"v2\"\"\n      }\n    }\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"cycling\"\", \n    \"\"user_id\"\": \"\"john_mcp_test\"\",\n    \"\"version\"\": \"\"v2\"\"\n  }')", "Bash(-d @/tmp/mcp_add_test.json)", "Bash(-d @/tmp/mcp_init.json)", "Bash(-d @/tmp/mcp_add_test2.json)", "Bash(-d @/tmp/mcp_search_test.json)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": \"\"测试contextual add v2功能：我正在验证MCP组件的修复结果\"\"\n      }\n    ],\n    \"\"user_id\"\": \"\"mcp_fix_test_user\"\",\n    \"\"version\"\": \"\"v2\"\"\n  }')", "Bash(-d '{\n    \"\"query\"\": \"\"验证MCP\"\",\n    \"\"filters\"\": {\n      \"\"user_id\"\": \"\"mcp_fix_test_user\"\"\n    },\n    \"\"version\"\": \"\"v2\"\",\n    \"\"limit\"\": 5\n  }')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"我正在通过MCP测试Mem0的contextual add v2功能\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"mcp_v2_test_user\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"MCP测试\"\",\n      \"\"user_id\"\": \"\"mcp_v2_test_user\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"initialize\"\",\n  \"\"params\"\": {\n    \"\"protocolVersion\"\": \"\"2025-03-26\"\",\n    \"\"capabilities\"\": {\n      \"\"tools\"\": {},\n      \"\"resources\"\": {},\n      \"\"prompts\"\": {},\n      \"\"logging\"\": {}\n    },\n    \"\"clientInfo\"\": {\n      \"\"name\"\": \"\"test-client\"\",\n      \"\"version\"\": \"\"1.0.0\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"method\"\": \"\"notifications/initialized\"\"\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 2,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"MCP测试\"\",\n      \"\"user_id\"\": \"\"mcp_v2_test_user\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"我还在测试图形记忆功能，<PERSON>和Sarah是兄妹关系\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"mcp_v2_test_user\"\",\n      \"\"version\"\": \"\"v2\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"最终测试：我正在验证MCP v2 contextual add的自动历史检索功能\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"mcp_final_test\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"测试HybridAdapter：我正在验证contextual add v2功能\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"hybrid_test_user\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"测试HybridAdapter v2功能：我正在验证contextual add自动历史检索\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"hybrid_test_user_v2\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 2,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"测试HybridAdapter v2功能：我正在验证contextual add自动历史检索\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"hybrid_test_user_v2\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 7,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"我和Sarah是朋友，她在谷歌工作做软件工程师\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"hybrid_mixed_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 11,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"咖啡\"\",\n      \"\"user_id\"\": \"\"hybrid_mixed_test_user\"\",\n      \"\"limit\"\": 10\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 14,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"<PERSON> plays badminton\"\"}],\n      \"\"user_id\"\": \"\"alice\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"What is <PERSON> favorite sport?\"\",\n      \"\"user_id\"\": \"\"alice\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"get_memories\"\",\n    \"\"arguments\"\": {\n      \"\"list_users\"\": true,\n      \"\"limit\"\": 10\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"<PERSON> is a cardiologist at Boston General Hospital. He graduated from Harvard Medical School and is married to <PERSON>, a nurse.\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"graph_test_user\"\", \n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"Alice\"\",\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}')", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"Alice\"\"}],\n    \"\"user_id\"\": \"\"graph_test_user\"\",\n    \"\"enable_graph\"\": true,\n    \"\"output_format\"\": \"\"v1.1\"\"\n  }')", "Bash(-G )", "Bash(-d \"query=Alice\" )", "Bash(-d \"user_id=graph_test_user\" )", "Bash(-d \"enable_graph=true\" )", "Bash(-d \"output_format=v1.1\")", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 5,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"get_memories\"\",\n    \"\"arguments\"\": {\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"get_memories\"\",\n    \"\"arguments\"\": {\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(# 第一步：初始化MCP会话\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"initialize\"\",\n  \"\"params\"\": {\n    \"\"protocolVersion\"\": \"\"2025-03-26\"\",\n    \"\"capabilities\"\": {\n      \"\"tools\"\": {},\n      \"\"resources\"\": {},\n      \"\"prompts\"\": {},\n      \"\"logging\"\": {}\n    },\n    \"\"clientInfo\"\": {\n      \"\"name\"\": \"\"test-client\"\",\n      \"\"version\"\": \"\"1.0.0\"\"\n    }\n  }\n}'' && echo -e \"\"\\n\\n--- Sending notification ---\"\"\n\n# 第二步：发送初始化完成通知\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"method\"\": \"\"notifications/initialized\"\"\n}'' && echo -e \"\"\\n\\n--- Testing get_memories ---\"\"\n\n# 第三步：测试get_memories\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 2,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"get_memories\"\",\n    \"\"arguments\"\": {\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}'')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"Alice\"\",\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}')", "Bash(# 使用MCP接口测试图形内存搜索\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"Alice\"\",\n      \"\"user_id\"\": \"\"graph_test_user\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}'')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 5,\n  \"\"method\"\": \"\"tools/list\"\"\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 6,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"测试搜索\"\",\n      \"\"user_id\"\": \"\"test_user_debug\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(# 第一步：初始化MCP会话\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"X-Session-ID: test-session-123\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"initialize\"\",\n  \"\"params\"\": {\n    \"\"protocolVersion\"\": \"\"2025-03-26\"\",\n    \"\"capabilities\"\": {\n      \"\"tools\"\": {},\n      \"\"resources\"\": {},\n      \"\"prompts\"\": {},\n      \"\"logging\"\": {}\n    },\n    \"\"clientInfo\"\": {\n      \"\"name\"\": \"\"test-client\"\",\n      \"\"version\"\": \"\"1.0.0\"\"\n    }\n  }\n}'' && echo -e \"\"\\n\\n--- Sending notification ---\"\"\n\n# 第二步：发送初始化完成通知\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"X-Session-ID: test-session-123\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"method\"\": \"\"notifications/initialized\"\"\n}'' && echo -e \"\"\\n\\n--- Testing tool call ---\"\"\n\n# 第三步：测试工具调用\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"X-Session-ID: test-session-123\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"测试搜索\"\",\n      \"\"user_id\"\": \"\"test_user_debug\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}'')", "Bash(# 测试搜索刚才添加的记忆\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"X-Session-ID: test-session-456\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"李明\"\",\n      \"\"user_id\"\": \"\"test_mcp_fix_user\"\",\n      \"\"limit\"\": 5\n    }\n  }\n}'')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\n          \"\"role\"\": \"\"user\"\", \n          \"\"content\"\": \"\"测试MCP 2025-06-18规范合规性的工具调用响应格式\"\"\n        }\n      ],\n      \"\"user_id\"\": \"\"mcp_compliance_test\"\"\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"测试搜索\"\",\n      \"\"user_id\"\": \"\"mcp_compliance_test\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}')", "Bash(-d '{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 6,\n  \"\"method\"\": \"\"ping\"\"\n}')", "Bash(# 模拟Cherry Studio客户端的直接工具调用格式\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 7,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"query\"\": \"\"测试Cherry Studio格式\"\",\n    \"\"user_id\"\": \"\"cherry_studio_test\"\",\n    \"\"limit\"\": 3\n  }\n}'')", "mcp__firecrawl-mcp__firecrawl_map", "Bash(# 测试带keyword_search的高级搜索\necho \"\"🔍 Testing Advanced Retrieval with keyword_search:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"寿司制作\"\",\n      \"\"user_id\"\": \"\"advanced_test_user\"\",\n      \"\"limit\"\": 5,\n      \"\"keyword_search\"\": true\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ntry:\n    data = json.load(sys.stdin)\n    if ''result'' in data and ''content'' in data[''result'']:\n        content = data[''result''][''content'']\n        for item in content:\n            if ''text'' in item and ''advanced_retrieval'' in item[''text'']:\n                print(''✅ Found advanced_retrieval config in response'')\n                # 检查keyword_search是否启用\n                if ''keyword_search\\\"\": true'' in item[''text'']:\n                    print(''✅ keyword_search: enabled'')\n                break\n        # 显示第一个结果\n        for item in content:\n            if ''Found'' in item.get(''text'', '''') and ''memories'' in item.get(''text'', ''''):\n                print(''✅ Advanced search result:'', item[''text''].split(''\\n'')[0])\n                break\n    else:\n        print(''❌ Advanced search failed'')\nexcept Exception as e:\n    print(''❌ Error:'', e)\"\")", "Bash(# 测试Contextual Add v2功能\necho \"\"🔍 Testing Contextual Add v2:\"\"\n\n# 第一个对话\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 5,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"嗨，我是Alex，住在旧金山。\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"你好Alex！很高兴认识你。旧金山是一个美丽的城市。\"\"}\n      ],\n      \"\"user_id\"\": \"\"contextual_v2_test\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}'' > /dev/null && echo \"\"✅ First conversation added with v2\"\"\n\n# 第二个对话 - 只发送新消息，v2会自动检索上下文\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 6,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我喜欢吃寿司，昨天我去了森尼韦尔和朋友一起吃寿司。\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"寿司真的是很美味的选择。你这个周末做了什么？\"\"}\n      ],\n      \"\"user_id\"\": \"\"contextual_v2_test\"\",\n      \"\"version\"\": \"\"v2\"\"\n    }\n  }\n}'' > /dev/null && echo \"\"✅ Second conversation added with v2 (contextual)\"\")", "Bash(# 验证v2的上下文关联效果\necho \"\"🔍 Verifying contextual memories:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 7,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"Alex\"\",\n      \"\"user_id\"\": \"\"contextual_v2_test\"\",\n      \"\"limit\"\": 5\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ndata = json.load(sys.stdin)\nif ''result'' in data:\n    content = data[''result''][''content'']\n    for item in content:\n        text = item.get(''text'', '''')\n        if ''Found'' in text and ''memories'' in text:\n            print(''✅ Found contextual memories:'', text.split(''\\n'')[0])\n            # 显示前2个记忆内容\n            lines = text.split(''\\n'')\n            for i, line in enumerate(lines[1:6]):  # 显示前几个结果\n                if line.strip() and (''Content:'' in line or ''Alex'' in line or ''旧金山'' in line or ''寿司'' in line):\n                    print(f''   {line.strip()}'')\n            break\n\"\")", "Bash(# 添加一些情感丰富的记忆用于Criteria Retrieval测试\necho \"\"🔍 Testing Criteria Retrieval - Adding emotional memories:\"\"\n\n# 添加快乐的记忆\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 8,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"今天是多么美丽的阳光明媚的一天！我感到神清气爽，准备迎接一切挑战！\"\"}\n      ],\n      \"\"user_id\"\": \"\"criteria_test_user\"\"\n    }\n  }\n}'' > /dev/null && echo \"\"✅ Added joyful memory\"\"\n\n# 添加好奇的记忆\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 9,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我一直想知道暴风雨是如何形成的——是什么在大气中触发了它们？\"\"}\n      ],\n      \"\"user_id\"\": \"\"criteria_test_user\"\"\n    }\n  }\n}'' > /dev/null && echo \"\"✅ Added curious memory\"\"\n\n# 添加负面情绪记忆\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 10,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"下了好几天的雨，这让一切都感觉更沉重了。\"\"}\n      ],\n      \"\"user_id\"\": \"\"criteria_test_user\"\"\n    }\n  }\n}'' > /dev/null && echo \"\"✅ Added sad memory\"\")", "Bash(# 测试Criteria Retrieval工具\necho \"\"🔍 Testing Criteria Retrieval Tool:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 11,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"criteria_retrieval\"\",\n    \"\"arguments\"\": {\n      \"\"user_id\"\": \"\"criteria_test_user\"\",\n      \"\"criteria\"\": {\n        \"\"query\"\": \"\"今天感觉如何？\"\",\n        \"\"retrieval_criteria\"\": [\n          {\n            \"\"name\"\": \"\"joy\"\",\n            \"\"description\"\": \"\"衡量句子中表达的积极情绪强度，如快乐、兴奋或愉悦。分数越高表示快乐程度越高。\"\",\n            \"\"weight\"\": 3\n          },\n          {\n            \"\"name\"\": \"\"curiosity\"\", \n            \"\"description\"\": \"\"评估句子反映好奇心、探索新信息兴趣或提问的程度。分数越高表示好奇心越强。\"\",\n            \"\"weight\"\": 2\n          },\n          {\n            \"\"name\"\": \"\"sadness\"\",\n            \"\"description\"\": \"\"评估悲伤或负面情绪色调的存在和深度，包括失望、沮丧或悲伤的表达。分数越高表示悲伤程度越高。\"\",\n            \"\"weight\"\": 1\n          }\n        ],\n        \"\"limit\"\": 5\n      }\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ntry:\n    data = json.load(sys.stdin)\n    if ''result'' in data and ''content'' in data[''result'']:\n        print(''✅ Criteria Retrieval Tool executed successfully'')\n        content = data[''result''][''content'']\n        for item in content:\n            text = item.get(''text'', '''')\n            if ''criteria'' in text.lower() and ''retrieval'' in text.lower():\n                print(''✅ Criteria-based retrieval performed'')\n                # 显示结果摘要\n                lines = text.split(''\\n'')[:10]\n                for line in lines:\n                    if line.strip() and (''joy'' in line or ''curiosity'' in line or ''Score'' in line):\n                        print(f''   {line.strip()}'')\n                break\n    else:\n        print(''❌ Criteria Retrieval failed:'', data.get(''error'', {}).get(''message'', ''Unknown error''))\nexcept Exception as e:\n    print(''❌ Error parsing response:'', e)\n\"\")", "Bash(# 综合功能测试脚本\ncat > /tmp/comprehensive_mem0_test.py << ''EOF''\n#!/usr/bin/env python3\n\"\"\"\"\"\"\nMem0官方功能综合测试 - MCP Server Version\n基于官方文档验证所有功能实现\n\"\"\"\"\"\"\n\nimport json\nimport requests\nimport sys\n\ndef test_mcp_request(method, params):\n    \"\"\"\"\"\"发送MCP请求\"\"\"\"\"\"\n    payload = {\n        \"\"jsonrpc\"\": \"\"2.0\"\",\n        \"\"id\"\": 1,\n        \"\"method\"\": method,\n        \"\"params\"\": params\n    }\n    \n    headers = {\n        \"\"Content-Type\"\": \"\"application/json\"\",\n        \"\"MCP-Protocol-Version\"\": \"\"2025-06-18\"\"\n    }\n    \n    try:\n        response = requests.post(\"\"http://localhost:8001/mcp\"\", \n                               json=payload, headers=headers, timeout=10)\n        return response.status_code == 200, response.json()\n    except Exception as e:\n        return False, {\"\"error\"\": str(e)}\n\ndef test_official_features():\n    \"\"\"\"\"\"测试Mem0官方功能\"\"\"\"\"\"\n    \n    print(\"\"🧪 Mem0 MCP Server - 官方功能综合测试\"\")\n    print(\"\"=\"\" * 60)\n    \n    test_results = {\n        \"\"basic_operations\"\": [],\n        \"\"advanced_features\"\": [],\n        \"\"contextual_features\"\": [],\n        \"\"graph_features\"\": []\n    }\n    \n    # 1. 基础操作测试\n    print(\"\"\\n1. 基础记忆操作测试\"\")\n    \n    # Add Memory (官方标准)\n    success, response = test_mcp_request(\"\"tools/call\"\", {\n        \"\"name\"\": \"\"add_memory\"\",\n        \"\"arguments\"\": {\n            \"\"messages\"\": [\n                {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我是张三，住在北京，喜欢编程和音乐。\"\"}\n            ],\n            \"\"user_id\"\": \"\"comprehensive_test\"\"\n        }\n    })\n    \n    if success and \"\"result\"\" in response:\n        print(\"\"✅ 基础添加记忆: PASS\"\")\n        test_results[\"\"basic_operations\"\"].append(\"\"add_memory: PASS\"\")\n    else:\n        print(\"\"❌ 基础添加记忆: FAIL\"\")\n        test_results[\"\"basic_operations\"\"].append(\"\"add_memory: FAIL\"\")\n    \n    # Search Memory (官方标准)\n    success, response = test_mcp_request(\"\"tools/call\"\", {\n        \"\"name\"\": \"\"search_memories\"\", \n        \"\"arguments\"\": {\n            \"\"query\"\": \"\"张三的爱好\"\",\n            \"\"user_id\"\": \"\"comprehensive_test\"\",\n            \"\"limit\"\": 5\n        }\n    })\n    \n    if success and \"\"result\"\" in response:\n        print(\"\"✅ 基础搜索记忆: PASS\"\")\n        test_results[\"\"basic_operations\"\"].append(\"\"search_memories: PASS\"\")\n    else:\n        print(\"\"❌ 基础搜索记忆: FAIL\"\")\n        test_results[\"\"basic_operations\"\"].append(\"\"search_memories: FAIL\"\")\n    \n    # 2. 高级检索功能测试 (Advanced Retrieval)\n    print(\"\"\\n2. 高级检索功能测试\"\")\n    \n    # Keyword Search + Rerank + Filter (官方文档功能)\n    success, response = test_mcp_request(\"\"tools/call\"\", {\n        \"\"name\"\": \"\"search_memories\"\",\n        \"\"arguments\"\": {\n            \"\"query\"\": \"\"编程\"\",\n            \"\"user_id\"\": \"\"comprehensive_test\"\",\n            \"\"keyword_search\"\": True,\n            \"\"rerank\"\": True,\n            \"\"filter_memories\"\": True,\n            \"\"limit\"\": 3\n        }\n    })\n    \n    if success and \"\"result\"\" in response:\n        print(\"\"✅ 高级检索 (keyword_search + rerank + filter): PASS\"\")\n        test_results[\"\"advanced_features\"\"].append(\"\"advanced_retrieval: PASS\"\")\n    else:\n        print(\"\"❌ 高级检索: FAIL\"\")\n        test_results[\"\"advanced_features\"\"].append(\"\"advanced_retrieval: FAIL\"\")\n    \n    # 3. Contextual Add v2功能测试\n    print(\"\"\\n3. Contextual Add v2功能测试\"\")\n    \n    # 第一轮对话\n    success, response = test_mcp_request(\"\"tools/call\"\", {\n        \"\"name\"\": \"\"add_memory\"\",\n        \"\"arguments\"\": {\n            \"\"messages\"\": [\n                {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"你好，我是李四。\"\"},\n                {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"你好李四！很高兴认识你。\"\"}\n            ],\n            \"\"user_id\"\": \"\"contextual_test\"\",\n            \"\"version\"\": \"\"v2\"\"\n        }\n    })\n    \n    if success:\n        # 第二轮对话 - 仅发送新消息，测试上下文自动检索\n        success2, response2 = test_mcp_request(\"\"tools/call\"\", {\n            \"\"name\"\": \"\"add_memory\"\", \n            \"\"arguments\"\": {\n                \"\"messages\"\": [\n                    {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我在上海工作，是一名数据科学家。\"\"}\n                ],\n                \"\"user_id\"\": \"\"contextual_test\"\",\n                \"\"version\"\": \"\"v2\"\"\n            }\n        })\n        \n        if success2 and \"\"result\"\" in response2:\n            print(\"\"✅ Contextual Add v2: PASS\"\")\n            test_results[\"\"contextual_features\"\"].append(\"\"contextual_add_v2: PASS\"\")\n        else:\n            print(\"\"❌ Contextual Add v2: FAIL\"\")\n            test_results[\"\"contextual_features\"\"].append(\"\"contextual_add_v2: FAIL\"\")\n    \n    # 4. 图形记忆功能测试\n    print(\"\"\\n4. 图形记忆功能测试\"\")\n    \n    success, response = test_mcp_request(\"\"tools/call\"\", {\n        \"\"name\"\": \"\"add_memory\"\",\n        \"\"arguments\"\": {\n            \"\"messages\"\": [\n                {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"Alice在OpenAI工作，她是一名机器学习工程师。她与Stanford的研究员Bob合作。\"\"}\n            ],\n            \"\"user_id\"\": \"\"graph_test\"\", \n            \"\"enable_graph\"\": True,\n            \"\"output_format\"\": \"\"v1.1\"\"\n        }\n    })\n    \n    if success and \"\"result\"\" in response:\n        print(\"\"✅ 图形记忆 (Graph Memory): PASS\"\")\n        test_results[\"\"graph_features\"\"].append(\"\"graph_memory: PASS\"\")\n    else:\n        print(\"\"❌ 图形记忆: FAIL\"\")\n        test_results[\"\"graph_features\"\"].append(\"\"graph_memory: FAIL\"\")\n    \n    # 5. 工具完整性验证\n    print(\"\"\\n5. 工具完整性验证\"\")\n    \n    success, response = test_mcp_request(\"\"tools/list\"\", {})\n    \n    if success and \"\"result\"\" in response and \"\"tools\"\" in response[\"\"result\"\"]:\n        tools = response[\"\"result\"\"][\"\"tools\"\"]\n        tool_names = [tool[\"\"name\"\"] for tool in tools]\n        \n        expected_tools = [\n            \"\"add_memory\"\", \"\"search_memories\"\", \"\"get_memories\"\", \n            \"\"get_memory_by_id\"\", \"\"delete_memory\"\", \"\"update_memory\"\",\n            \"\"batch_delete_memories\"\", \"\"manage_graph_entities\"\",\n            \"\"manage_graph_relationships\"\", \"\"selective_memory\"\", \"\"criteria_retrieval\"\"\n        ]\n        \n        missing_tools = [tool for tool in expected_tools if tool not in tool_names]\n        \n        if not missing_tools:\n            print(f\"\"✅ 工具完整性: PASS ({len(tools)} tools available)\"\")\n        else:\n            print(f\"\"❌ 工具完整性: FAIL (missing: {missing_tools})\"\")\n    \n    # 测试总结\n    print(\"\"\\n\"\" + \"\"=\"\" * 60)\n    print(\"\"🎯 测试总结:\"\")\n    \n    total_tests = 0\n    passed_tests = 0\n    \n    for category, results in test_results.items():\n        total_tests += len(results)\n        passed_tests += len([r for r in results if \"\"PASS\"\" in r])\n        print(f\"\"  {category}: {results}\"\")\n    \n    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0\n    print(f\"\"\\n✅ 总体通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)\"\")\n    \n    if success_rate >= 80:\n        print(\"\"🎉 Mem0 MCP服务器与官方功能文档高度一致!\"\")\n        return True\n    else:\n        print(\"\"⚠️  部分功能需要进一步完善\"\")\n        return False\n\nif __name__ == \"\"__main__\"\":\n    success = test_official_features()\n    sys.exit(0 if success else 1)\nEOF\n\npython3 /tmp/comprehensive_mem0_test.py)", "Bash(# 测试核心MCP协议功能\necho \"\"🧪 核心MCP功能快速验证:\"\"\n\n# 1. 测试协议初始化\necho -e \"\"\\n1. 协议初始化测试:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"initialize\"\", \n  \"\"params\"\": {\n    \"\"protocolVersion\"\": \"\"2025-06-18\"\",\n    \"\"capabilities\"\": {\"\"tools\"\": {}, \"\"resources\"\": {}, \"\"prompts\"\": {}},\n    \"\"clientInfo\"\": {\"\"name\"\": \"\"test-client\"\", \"\"version\"\": \"\"1.0.0\"\"}\n  }\n}'' | python3 -c \"\"\nimport json, sys\ndata = json.load(sys.stdin)\nif ''result'' in data and ''protocolVersion'' in data[''result'']:\n    print(''✅ 协议初始化成功:'', data[''result''][''protocolVersion''])\nelse:\n    print(''❌ 协议初始化失败'')\n\"\"\n\n# 2. 测试工具列表\necho -e \"\"\\n2. 工具列表测试:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 2,\n  \"\"method\"\": \"\"tools/list\"\"\n}'' | python3 -c \"\"\nimport json, sys\ndata = json.load(sys.stdin)\nif ''result'' in data and ''tools'' in data[''result'']:\n    tool_count = len(data[''result''][''tools''])\n    print(f''✅ 工具列表获取成功: {tool_count} 个工具'')\nelse:\n    print(''❌ 工具列表获取失败'')\n\"\"\n\n# 3. 测试基础记忆添加\necho -e \"\"\\n3. 基础记忆添加测试:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 3,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"最终验证测试: 我喜欢喝咖啡\"\"}],\n      \"\"user_id\"\": \"\"final_verification_test\"\"\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ndata = json.load(sys.stdin)\nif ''result'' in data and ''content'' in data[''result'']:\n    print(''✅ 记忆添加成功'')\nelse:\n    print(''❌ 记忆添加失败'')\n\"\"\n\n# 4. 测试记忆搜索\necho -e \"\"\\n4. 记忆搜索测试:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\", \n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"咖啡\"\",\n      \"\"user_id\"\": \"\"final_verification_test\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ndata = json.load(sys.stdin)\nif ''result'' in data and ''content'' in data[''result'']:\n    print(''✅ 记忆搜索成功'')\nelse:\n    print(''❌ 记忆搜索失败'')\n\"\"\n\necho -e \"\"\\n🎯 核心功能验证完成\"\")", "Bash(__NEW_LINE__ echo -e \"\\n🔄 启动MCP服务器:\")", "Bash(# 先测试MCP协议初始化\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"initialize\"\",\n  \"\"params\"\": {\n    \"\"protocolVersion\"\": \"\"2025-06-18\"\",\n    \"\"capabilities\"\": {\"\"tools\"\": {}, \"\"resources\"\": {}, \"\"prompts\"\": {}},\n    \"\"clientInfo\"\": {\"\"name\"\": \"\"test-client\"\", \"\"version\"\": \"\"1.0.0\"\"}\n  }\n}'' && echo -e \"\"\\n\"\"\n\n# 发送初始化完成通知\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\", \n  \"\"method\"\": \"\"notifications/initialized\"\"\n}'' && echo -e \"\"\\n\"\")", "Bash(# 测试图谱记忆添加 - 按照官方文档要求\necho \"\"🧪 图谱记忆功能测试 - 官方文档示例:\"\"\n\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"Hello <PERSON>, its nice to meet you!\"\"},\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"Im from Seattle and I work as a software engineer\"\"}\n      ],\n      \"\"user_id\"\": \"\"joseph_graph_test\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}'' | python3 -c \"\"\nimport json, sys\ntry:\n    data = json.load(sys.stdin)\n    if ''result'' in data:\n        print(''✅ 图谱记忆添加请求成功发送'')\n        result = data[''result'']\n        if ''content'' in result:\n            for item in result[''content'']:\n                if ''text'' in item and ''Added'' in item[''text'']:\n                    print(''   📝 记忆已添加，图谱处理中...'')\n                    break\n        print(''   📊 HybridAdapter应该已路由到v1端点进行图谱处理'')\n    else:\n        print(''❌ 请求失败:'', data.get(''error'', {}).get(''message'', ''Unknown error''))\nexcept Exception as e:\n    print(''❌ JSON解析错误:'', str(e))\n    # 显示原始响应\n    print(''原始响应:'', sys.stdin.read() if hasattr(sys.stdin, ''read'') else ''No data'')\n\"\")", "Bash(# 直接查看MCP响应，不解析JSON\necho \"\"🔍 直接测试MCP图谱记忆响应:\"\"\n\ncurl -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"Hello <PERSON>, its nice to meet you!\"\"},\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"Im from Seattle and I work as a software engineer\"\"}\n      ],\n      \"\"user_id\"\": \"\"joseph_graph_test\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}'')", "Bash(# 修复JSON格式问题，使用正确的引号\necho \"\"🔧 修复JSON格式并测试图谱记忆功能:\"\"\n\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"Hello <PERSON>, it is nice to meet you!\"\"},\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I am from Seattle and I work as a software engineer\"\"}\n      ],\n      \"\"user_id\"\": \"\"joseph_graph_test\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}'' && echo \"\"\"\")", "Bash(# 创建正确格式的测试JSON文件\ncat > /tmp/graph_memory_test.json << ''EOF''\n{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 1,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"add_memory\"\",\n    \"\"arguments\"\": {\n      \"\"messages\"\": [\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>\"\"},\n        {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"Hello Joseph, it is nice to meet you!\"\"},\n        {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I am from Seattle and I work as a software engineer\"\"}\n      ],\n      \"\"user_id\"\": \"\"joseph_graph_test\"\",\n      \"\"enable_graph\"\": true,\n      \"\"output_format\"\": \"\"v1.1\"\"\n    }\n  }\n}\nEOF\n\necho \"\"🔍 验证修复后的JSON格式:\"\"\npython3 -c \"\"import json; print(''✅ JSON格式正确'' if json.load(open(''/tmp/graph_memory_test.json'')) else ''❌ JSON格式错误'')\"\"\n\necho -e \"\"\\n🧪 测试图谱记忆添加:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"MCP-Protocol-Version: 2025-06-18\"\" \\\n  -d @/tmp/graph_memory_test.json)", "Bash(# 使用简单的内联JSON，避免文件转义问题\necho \"\"🧪 测试图谱记忆功能 - 直接API调用:\"\"\n\n# 先测试基础工具调用\necho \"\"1. 测试基础记忆添加:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"id\"\": 1, \"\"method\"\": \"\"tools/call\"\", \"\"params\"\": {\"\"name\"\": \"\"add_memory\"\", \"\"arguments\"\": {\"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"Test basic memory\"\"}], \"\"user_id\"\": \"\"test_basic\"\"}}}'')", "Bash(# 完整的MCP图谱记忆测试流程\necho \"\"🧪 完整MCP图谱记忆测试:\"\"\n\necho \"\"1. 初始化MCP会话:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"id\"\": 1, \"\"method\"\": \"\"initialize\"\", \"\"params\"\": {\"\"protocolVersion\"\": \"\"2025-06-18\"\", \"\"capabilities\"\": {\"\"tools\"\": {}}, \"\"clientInfo\"\": {\"\"name\"\": \"\"test-client\"\", \"\"version\"\": \"\"1.0.0\"\"}}}'' && echo \"\"\"\"\n\necho \"\"2. 发送初始化完成通知:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"method\"\": \"\"notifications/initialized\"\"}'' && echo \"\"\"\"\n\necho \"\"3. 测试图谱记忆添加 (enable_graph=true):\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"id\"\": 3, \"\"method\"\": \"\"tools/call\"\", \"\"params\"\": {\"\"name\"\": \"\"add_memory\"\", \"\"arguments\"\": {\"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is Joseph\"\"}, {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I am from Seattle and work as a software engineer\"\"}], \"\"user_id\"\": \"\"joseph_test\"\", \"\"enable_graph\"\": true, \"\"output_format\"\": \"\"v1.1\"\"}}}'' && echo \"\"\"\")", "Bash(# 直接查看搜索响应\necho \"\"🔍 测试图谱记忆搜索 - 直接响应:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"id\"\": 4, \"\"method\"\": \"\"tools/call\"\", \"\"params\"\": {\"\"name\"\": \"\"search_memories\"\", \"\"arguments\"\": {\"\"query\"\": \"\"Joseph\"\", \"\"user_id\"\": \"\"joseph_test\"\", \"\"enable_graph\"\": true, \"\"output_format\"\": \"\"v1.1\"\"}}}'')", "Bash(# 直接查看get_memories响应\necho \"\"🔍 测试get_memories图谱功能 - 直接响应:\"\"\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\"\"jsonrpc\"\": \"\"2.0\"\", \"\"id\"\": 5, \"\"method\"\": \"\"tools/call\"\", \"\"params\"\": {\"\"name\"\": \"\"get_memories\"\", \"\"arguments\"\": {\"\"user_id\"\": \"\"joseph_test\"\", \"\"enable_graph\"\": true, \"\"output_format\"\": \"\"v1.1\"\", \"\"limit\"\": 3}}}'')", "Bash(__NEW_LINE__ echo -e \"\\n📊 测试结果总结:\")", "Bash(__NEW_LINE__ echo -e \"\\n✅ search_memories (图谱模式): 完全成功\")", "Bash(__NEW_LINE__ echo -e \"\\n✅ get_memories (图谱模式): 完全成功\")", "Bash(__NEW_LINE__ echo -e \"\\n🔧 HybridAdapter智能路由验证:\")", "Bash(__NEW_LINE__ echo -e \"\\n🏆 结论:\")", "Bash(# 直接查看搜索结果\ncurl -s -X POST http://localhost:8001/mcp \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n  \"\"jsonrpc\"\": \"\"2.0\"\",\n  \"\"id\"\": 4,\n  \"\"method\"\": \"\"tools/call\"\",\n  \"\"params\"\": {\n    \"\"name\"\": \"\"search_memories\"\",\n    \"\"arguments\"\": {\n      \"\"query\"\": \"\"yoga meditation\"\",\n      \"\"user_id\"\": \"\"david_mcp_categories_test\"\",\n      \"\"limit\"\": 3\n    }\n  }\n}'')", "Bash(__NEW_LINE__ echo -e \"\\n1. ✅ 官方文档理解:\")", "Bash(__NEW_LINE__ echo -e \"\\n2. ✅ API实现验证:\")", "Bash(__NEW_LINE__ echo -e \"\\n3. ✅ MCP接口测试:\")", "Bash(__NEW_LINE__ echo -e \"\\n4. 🔍 实现状态分析:\")", "Bash(__NEW_LINE__ echo -e \"\\n5. 🎯 与官方文档的一致性:\")", "Bash(__NEW_LINE__ echo -e \"\\n🔍 三个工具的核心功能对比:\")", "Bash(__NEW_LINE__ echo -e \"\\n1. search_memories (现有核心搜索工具)\")", "Bash(__NEW_LINE__ echo -e \"\\n2. selective_memory (重要性评估工具)\")", "Bash(__NEW_LINE__ echo -e \"\\n3. criteria_retrieval (复杂条件检索工具)\")", "Bash(__NEW_LINE__ echo -e \"\\n🔄 search_memories 与 criteria_retrieval 重叠:\")", "Bash(__NEW_LINE__ echo -e \"\\n🔄 search_memories 与 selective_memory 重叠:\")", "Bash(__NEW_LINE__ echo -e \"\\n🔄 criteria_retrieval 与 selective_memory 重叠:\")", "Bash(__NEW_LINE__ echo -e \"\\n💡 方案一: 完全整合到search_memories\")", "Bash(__NEW_LINE__ echo -e \"\\n💡 方案二: 保留所有工具但重新设计\")", "Bash(__NEW_LINE__ echo -e \"\\n💡 方案三: 部分整合（推荐方案）\")"], "deny": []}}